import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:image_cropper/image_cropper.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:dio/dio.dart';
import 'package:starchex/app/data/BPCommonModels.dart';
import 'package:starchex/app/data/BPUserModels.dart';
import 'package:starchex/app/modules/Splash/controllers/splash_controller.dart';
import 'package:starchex/app/shared/components/BPDeviceUtil.dart';
import 'package:starchex/app/shared/components/BPEasyLoading.dart';
import 'package:starchex/app/shared/components/BPColorUtil.dart';
import 'package:starchex/app/shared/components/mv_text.dart';
import 'package:starchex/app/shared/components/mv_text_button.dart';
import 'package:starchex/app/shared/components/mv_uploader.dart';
import 'package:starchex/app/shared/account/BPSessionManager.dart';
import 'package:starchex/app/shared/networking/BPRestAPI.dart';
import 'package:starchex/generated/locales.g.dart';

// AI生成步骤枚举
enum AIGenerationStep {
  none, // 未开始
  photoSelected, // 照片已选择，等待用户决定
  submitting, // 提交任务中
  pending, // 任务已提交，等待处理
  processing, // AI生成中
  completed, // 生成完成
  failed, // 生成失败
  cancelled, // 用户取消
}

class AvatarGenerationController extends GetxController {
  final rxAvatarImageUpload = Rx<BPFileUpload<BPFile>?>(null);
  final rxSplashImageUpload = Rx<BPFileUpload<BPFile>?>(null);

  // AI生成相关状态
  final aiGenerationStep = AIGenerationStep.none.obs;
  final aiGenerationTaskId = ''.obs;
  final aiGenerationProgress = 0.0.obs;
  final showAIGenerationDialog = false.obs;
  final estimatedTimeRemaining = 0.obs;
  final canCancelGeneration = true.obs;

  final isAIGenerationInProgress = false.obs;
  final aiGenerationStatusText = ''.obs;
  final aiAvatarUrl = ''.obs;
  final aiOriginalImageUrl = ''.obs;
  final originalPhotoFile = Rx<BPFile?>(null);

  // 当前显示的头像
  final currentAvatarUrl = ''.obs;

  BPUserModel? get _accountModel => BPSessionManager.accountModelConvenience;

  // 验证是否可以保存
  bool get isValid => rxAvatarImageUpload.value != null || aiOriginalImageUrl.value.isNotEmpty;

  // 是否正在提交中
  bool get isSubmitting => _submitTask.isLoading || _uploader.isUploading;

  final _uploader = MVUploader();
  final _aiGenerateTask = MVTaskToken();
  final _submitTask = MVTaskToken();
  Timer? _statusPollingTimer;
  Timer? _countdownTimer;

  /// 判断AI头像是否是base64格式
  bool get isAIAvatarBase64 {
    return aiAvatarUrl.value.isNotEmpty && aiAvatarUrl.value.startsWith('data:image/');
  }

  /// 从base64 data URI中提取base64字符串并解码为Uint8List
  Uint8List? get aiAvatarBytes {
    if (!isAIAvatarBase64) return null;

    try {
      final dataUri = aiAvatarUrl.value;
      final base64Str = dataUri.substring(dataUri.indexOf(',') + 1);
      return base64Decode(base64Str);
    } catch (e) {
      if (kDebugMode) {
        print('解码AI头像base64失败: $e');
      }
      return null;
    }
  }

  @override
  void onInit() {
    super.onInit();

    // 初始化时显示用户当前头像
    _loadCurrentAvatar();

    // 检查是否有进行中的AI生成任务
    _checkPendingAIGeneration();
  }

  @override
  void onClose() {
    _aiGenerateTask.cancel();
    _submitTask.cancel();
    _uploader.dispose();
    _statusPollingTimer?.cancel();
    _countdownTimer?.cancel();
    super.onClose();
  }

  void _loadCurrentAvatar() {
    final user = _accountModel;
    if (user?.fakeAvatar?.url != null) {
      currentAvatarUrl.value = user!.fakeAvatar!.url!;
    }
  }

  // AI Avatar按钮点击
  void onAIAvatarClicked() {
    if (isAIGenerationInProgress.value) {
      MVToast.showError(LocaleKeys.sc_str_ai_avatar_generating.tr);
      return;
    }

    BPDeviceUtil.pickPhotoFromAlbum(
      cropIfIO: false,
      completion: (file) {
        if (file != null) {
          originalPhotoFile.value = file;
          aiGenerationStep.value = AIGenerationStep.photoSelected;
          _showAvatarChoiceDialog();
        }
      },
    );
  }

  void _showAvatarChoiceDialog() {
    Get.dialog(
      AlertDialog(
        backgroundColor: BPColor.background,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
        title: MVText(
          LocaleKeys.sc_str_community_register_tip.tr,
          color: BPColor.whiteText,
          fontSize: 18,
          fontWeight: FontWeight.bold,
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // 预览区域
            Container(
              width: 120,
              height: 120,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(60),
                border: Border.all(color: BPColor.gold, width: 2),
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(58),
                child: _buildOriginalPhotoPreview(),
              ),
            ),
            const SizedBox(height: 16),
            MVText(
              LocaleKeys.sc_str_community_register_tip.tr,
              color: BPColor.greyText,
              fontSize: 14,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 20),

            // 选项按钮
            Column(
              children: [
                SizedBox(
                  width: double.infinity,
                  child: MVTextButton(
                    title: LocaleKeys.sc_str_ai_avatar_generating.tr,
                    backgroundColor: BPColor.gold,
                    textColor: BPColor.whiteText,
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    onPressed: () {
                      Get.back();
                      _startAsyncAIGeneration();
                    },
                    borderRadius: 25,
                    padding: const EdgeInsets.symmetric(vertical: 14),
                  ),
                ),
                const SizedBox(height: 12),
                // SizedBox(
                //   width: double.infinity,
                //   child: MVTextButton(
                //     title: LocaleKeys.sc_str_ai_avatar_crop_avatar.tr,
                //     backgroundColor: Colors.transparent,
                //     borderSideColor: BPColor.greyText,
                //     borderSideWidth: 1,
                //     textColor: BPColor.whiteText,
                //     fontSize: 16,
                //     onPressed: () {
                //       Get.back();
                //       _cropAndUseOriginal();
                //     },
                //     borderRadius: 25,
                //     padding: const EdgeInsets.symmetric(vertical: 14),
                //   ),
                // ),
                // const SizedBox(height: 12),
                SizedBox(
                  width: double.infinity,
                  child: MVTextButton(
                    title: LocaleKeys.common_cancel.tr,
                    backgroundColor: Colors.transparent,
                    borderSideColor: BPColor.greyText,
                    borderSideWidth: 1,
                    textColor: BPColor.whiteText,
                    fontSize: 16,
                    onPressed: () {
                      Get.back();
                    },
                    borderRadius: 25,
                    padding: const EdgeInsets.symmetric(vertical: 14),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
      barrierDismissible: false,
    );
  }

  Widget _buildOriginalPhotoPreview() {
    if (originalPhotoFile.value == null) return const SizedBox.shrink();

    if (kIsWeb) {
      return Image.memory(
        originalPhotoFile.value!.bytes,
        fit: BoxFit.cover,
      );
    } else {
      return Image.file(
        originalPhotoFile.value!.file!,
        fit: BoxFit.cover,
      );
    }
  }

  void _cropAndUseOriginal() async {
    if (originalPhotoFile.value == null) return;

    try {
      if (kIsWeb) {
        // Web端直接使用原图
        rxAvatarImageUpload.value = BPFileUpload(fileSelected: originalPhotoFile.value!);
        _updateAvatar();
        MVToast.showSuccess(LocaleKeys.common_done.tr);
        return;
      }

      // 移动端进行裁切
      CroppedFile? croppedFile = await ImageCropper().cropImage(
        maxWidth: 1000,
        maxHeight: 1000,
        sourcePath: originalPhotoFile.value!.file!.path,
        aspectRatio: const CropAspectRatio(ratioX: 1, ratioY: 1),
        uiSettings: [
          AndroidUiSettings(
            toolbarTitle: LocaleKeys.sc_str_ai_avatar_crop_avatar.tr,
            toolbarColor: Colors.deepOrange,
            toolbarWidgetColor: Colors.white,
            initAspectRatio: CropAspectRatioPreset.square,
            lockAspectRatio: true,
          ),
          IOSUiSettings(
            title: LocaleKeys.sc_str_ai_avatar_crop_avatar.tr,
            doneButtonTitle: LocaleKeys.common_done.tr,
            cancelButtonTitle: LocaleKeys.common_cancel.tr,
            aspectRatioPresets: [CropAspectRatioPreset.square],
          ),
        ],
      );

      if (croppedFile != null) {
        final croppedBytes = await croppedFile.readAsBytes();
        final croppedPath = croppedFile.path;

        rxAvatarImageUpload.value = BPFileUpload(
          fileSelected: BPFile(
            name: _generateFileName(filePath: croppedPath, bytes: croppedBytes),
            file: File(croppedPath),
            bytes: croppedBytes,
          ),
        );

        _updateAvatar();
        MVToast.showSuccess(LocaleKeys.sc_str_ai_avatar_error_crop_success.tr);
      }
    } catch (e) {
      MVToast.showError('${LocaleKeys.sc_str_ai_avatar_error_crop_failed.tr}: ${e.toString()}');
    }
  }

  void _startAsyncAIGeneration() async {
    if (originalPhotoFile.value == null) {
      MVToast.showError(LocaleKeys.sc_str_ai_avatar_error_upload_first.tr);
      return;
    }

    // 设置生成中状态
    isAIGenerationInProgress.value = true;
    aiGenerationStatusText.value = LocaleKeys.sc_str_ai_avatar_generating.tr;

    aiGenerationStep.value = AIGenerationStep.submitting;
    showAIGenerationDialog.value = true;
    canCancelGeneration.value = true;

    final idf = _aiGenerateTask.create();
    try {
      // 准备base64图片数据
      String base64Image;
      if (kIsWeb) {
        base64Image = base64Encode(originalPhotoFile.value!.bytes);
      } else {
        final bytes = await originalPhotoFile.value!.file!.readAsBytes();
        base64Image = base64Encode(bytes);
      }

      // 调用异步生成接口
      final result = await BPRestClient().generateAIAvatarAsync(
        body: {'imageBase64': base64Image},
        cancelToken: _aiGenerateTask.task,
      );

      if (result['success'] == true && result['taskId'] != null) {
        aiGenerationTaskId.value = result['taskId'];
        aiGenerationStep.value = AIGenerationStep.pending;
        aiGenerationStatusText.value = LocaleKeys.sc_str_ai_avatar_wait_time.tr;

        // 存储任务ID到本地
        final userId = _accountModel?.id;
        if (userId != null) {
          await _storeTaskId(userId, result['taskId']);
        }

        // 开始10秒倒计时
        _startCountdown(10);

        MVToast.showSuccess(LocaleKeys.sc_str_ai_avatar_generating.tr);
      } else {
        throw Exception('Failed to submit AI generation task');
      }
    } catch (e) {
      aiGenerationStep.value = AIGenerationStep.failed;
      showAIGenerationDialog.value = false;
      _resetAIGenerationState();
      catchedError(e);
    } finally {
      _aiGenerateTask.completed(idf: idf);
    }
  }

  void _startCountdown(int seconds) {
    estimatedTimeRemaining.value = seconds;
    _countdownTimer?.cancel();

    _countdownTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (estimatedTimeRemaining.value > 0) {
        estimatedTimeRemaining.value--;
      } else {
        timer.cancel();
        _startStatusPolling();
      }
    });
  }

  void _startStatusPolling() {
    if (aiGenerationTaskId.value.isEmpty) return;

    _statusPollingTimer?.cancel();
    _statusPollingTimer = Timer.periodic(const Duration(seconds: 3), (timer) {
      _checkTaskStatus();
    });
  }

  Future<void> _checkTaskStatus() async {
    if (aiGenerationTaskId.value.isEmpty) return;

    try {
      final result = await BPRestClient().getAIAvatarTaskStatus(
        taskId: aiGenerationTaskId.value,
      );

      if (result['success'] == true) {
        final status = result['status'];

        switch (status) {
          case 'pending':
            aiGenerationStep.value = AIGenerationStep.pending;
            aiGenerationStatusText.value = LocaleKeys.sc_str_ai_avatar_wait_time.tr;
            break;
          case 'processing':
            aiGenerationStep.value = AIGenerationStep.processing;
            aiGenerationProgress.value = 0.5;
            aiGenerationStatusText.value = LocaleKeys.sc_str_ai_avatar_generating.tr;
            break;
          case 'completed':
            aiGenerationStep.value = AIGenerationStep.completed;
            aiGenerationProgress.value = 1.0;
            aiGenerationStatusText.value = LocaleKeys.sc_str_ai_avatar_completed.tr;

            if (result['imageBase64'] != null) {
              final aiImageUrl = 'data:image/jpeg;base64,${result['imageBase64']}';
              aiOriginalImageUrl.value = aiImageUrl;
              // 更新当前显示的头像
              currentAvatarUrl.value = aiImageUrl;
            }

            _stopPolling();

            // 清理存储的任务ID
            final userId = _accountModel?.id;
            if (userId != null) {
              await _clearStoredTaskId(userId);
            }

            _resetAIGenerationState();
            _showAICompletionDialog();
            break;
          case 'failed':
            aiGenerationStep.value = AIGenerationStep.failed;
            _stopPolling();

            final userId = _accountModel?.id;
            if (userId != null) {
              await _clearStoredTaskId(userId);
            }

            _resetAIGenerationState();
            MVToast.showError(result['error'] ?? LocaleKeys.sc_str_ai_avatar_generation_failed.tr);
            break;
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('Status polling error: $e');
      }
    }
  }

  void _stopPolling() {
    _statusPollingTimer?.cancel();
    _countdownTimer?.cancel();
    canCancelGeneration.value = false;
  }

  void _showAICompletionDialog() {
    showAIGenerationDialog.value = false;

    Get.dialog(
      AlertDialog(
        backgroundColor: BPColor.background,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
        title: MVText(
          LocaleKeys.sc_str_ai_avatar_completed.tr,
          color: BPColor.whiteText,
          fontSize: 18,
          fontWeight: FontWeight.bold,
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // AI头像预览
            Container(
              width: 150,
              height: 150,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(75),
                border: Border.all(color: BPColor.gold, width: 3),
                boxShadow: [
                  BoxShadow(
                    color: BPColor.gold.withOpacity(0.3),
                    blurRadius: 15,
                    spreadRadius: 2,
                  ),
                ],
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(72),
                child: _buildAIOriginalImagePreview(),
              ),
            ),
            const SizedBox(height: 20),
            MVText(
              LocaleKeys.sc_str_ai_avatar_generation_success.tr,
              color: BPColor.greyText,
              fontSize: 14,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),

            // 操作按钮
            Column(
              children: [
                Row(
                  children: [
                    Expanded(
                      child: MVTextButton(
                        title: LocaleKeys.sc_str_ai_avatar_regenerate.tr,
                        backgroundColor: Colors.transparent,
                        borderSideColor: BPColor.greyText,
                        borderSideWidth: 1,
                        textColor: BPColor.whiteText,
                        fontSize: 14,
                        onPressed: () {
                          Get.back();
                          _startAsyncAIGeneration();
                        },
                        borderRadius: 25,
                        padding: const EdgeInsets.symmetric(vertical: 12),
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: MVTextButton(
                        title: LocaleKeys.sc_str_ai_avatar_crop_avatar.tr,
                        backgroundColor: BPColor.gold,
                        textColor: BPColor.whiteText,
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                        onPressed: () {
                          Get.back();
                          _cropAIAvatarForUpload();
                        },
                        borderRadius: 25,
                        padding: const EdgeInsets.symmetric(vertical: 12),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                SizedBox(
                  width: double.infinity,
                  child: MVTextButton(
                    title: LocaleKeys.common_cancel.tr,
                    backgroundColor: Colors.transparent,
                    borderSideColor: BPColor.greyText,
                    borderSideWidth: 1,
                    textColor: BPColor.whiteText,
                    fontSize: 14,
                    onPressed: () {
                      Get.back();
                      // 清除AI生成的结果，恢复到初始状态
                      aiOriginalImageUrl.value = '';
                      currentAvatarUrl.value = '';
                      // 恢复到用户原始头像
                      _loadCurrentAvatar();
                    },
                    borderRadius: 25,
                    padding: const EdgeInsets.symmetric(vertical: 12),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
      barrierDismissible: false,
    );
  }

  Widget _buildAIOriginalImagePreview() {
    if (aiOriginalImageUrl.value.isEmpty) return const SizedBox.shrink();

    if (aiOriginalImageUrl.value.startsWith('data:image/')) {
      try {
        final dataUri = aiOriginalImageUrl.value;
        final base64Str = dataUri.substring(dataUri.indexOf(',') + 1);
        final bytes = base64Decode(base64Str);
        return Image.memory(
          bytes,
          fit: BoxFit.cover,
          errorBuilder: (context, error, stackTrace) {
            return const Icon(Icons.error_outline, color: BPColor.greyText, size: 40);
          },
        );
      } catch (e) {
        return const Icon(Icons.error_outline, color: BPColor.greyText, size: 40);
      }
    }

    return Image.network(
      aiOriginalImageUrl.value,
      fit: BoxFit.cover,
      errorBuilder: (context, error, stackTrace) {
        return const Icon(Icons.error_outline, color: BPColor.greyText, size: 40);
      },
    );
  }

  Future<void> _cropAIAvatarForUpload() async {
    if (aiOriginalImageUrl.value.isEmpty) {
      MVToast.showError(LocaleKeys.sc_str_ai_avatar_error_no_croppable_avatar.tr);
      return;
    }

    if (kIsWeb) {
      MVToast.showError(LocaleKeys.sc_str_ai_avatar_error_web_not_supported.tr);
      return;
    }

    try {
      Uint8List bytes;

      if (aiOriginalImageUrl.value.startsWith('data:image/')) {
        try {
          final dataUri = aiOriginalImageUrl.value;
          final base64Str = dataUri.substring(dataUri.indexOf(',') + 1);
          bytes = base64Decode(base64Str);
        } catch (e) {
          MVToast.showError(LocaleKeys.sc_str_ai_avatar_error_data_format_error.tr);
          return;
        }
      } else {
        try {
          final dio = Dio();
          final response = await dio.get(
            aiOriginalImageUrl.value,
            options: Options(responseType: ResponseType.bytes),
          );
          if (response.statusCode == 200) {
            bytes = Uint8List.fromList(response.data);
          } else {
            MVToast.showError('${LocaleKeys.sc_str_ai_avatar_error_download_failed_code.tr}: ${response.statusCode}');
            return;
          }
        } catch (e) {
          MVToast.showError('${LocaleKeys.sc_str_ai_avatar_error_download_failed.tr}: $e');
          return;
        }
      }

      // 创建临时文件
      final tempDir = await Directory.systemTemp.createTemp('ai_avatar_');
      final tempFile = File('${tempDir.path}/ai_avatar.jpg');
      await tempFile.writeAsBytes(bytes);

      // 裁切图片
      CroppedFile? croppedFile = await ImageCropper().cropImage(
        maxWidth: 1000,
        maxHeight: 1000,
        sourcePath: tempFile.path,
        aspectRatio: const CropAspectRatio(ratioX: 1, ratioY: 1),
        uiSettings: [
          AndroidUiSettings(
            toolbarTitle: LocaleKeys.sc_str_ai_avatar_crop_avatar.tr,
            toolbarColor: Colors.deepOrange,
            toolbarWidgetColor: Colors.white,
            initAspectRatio: CropAspectRatioPreset.square,
            lockAspectRatio: true,
          ),
          IOSUiSettings(
            title: LocaleKeys.sc_str_ai_avatar_crop_avatar.tr,
            doneButtonTitle: LocaleKeys.common_done.tr,
            cancelButtonTitle: LocaleKeys.common_cancel.tr,
            aspectRatioPresets: [CropAspectRatioPreset.square],
          ),
        ],
      );

      if (croppedFile != null) {
        final croppedBytes = await croppedFile.readAsBytes();
        final croppedPath = croppedFile.path;

        // 设置裁切后的图片为头像上传
        rxAvatarImageUpload.value = BPFileUpload(
          fileSelected: BPFile(
            name: _generateFileName(filePath: croppedPath, bytes: croppedBytes),
            file: File(croppedPath),
            bytes: croppedBytes,
          ),
        );

        // 为splashImage创建单独的临时文件
        final splashTempDir = await Directory.systemTemp.createTemp('ai_splash_');
        final splashTempFile = File('${splashTempDir.path}/ai_splash.jpg');
        await splashTempFile.writeAsBytes(bytes);

        // 同时准备AI原始图片作为splashImage上传
        rxSplashImageUpload.value = BPFileUpload(
          fileSelected: BPFile(
            name: _generateFileName(fileName: 'ai_splash_${DateTime.now().millisecondsSinceEpoch}.jpg', bytes: bytes),
            file: splashTempFile,
            bytes: bytes,
          ),
        );

        _updateAvatar();
        MVToast.showSuccess(LocaleKeys.sc_str_ai_avatar_error_crop_success.tr);
      }

      // 清理裁切用的临时文件
      await tempDir.delete(recursive: true);
    } catch (e) {
      if (kDebugMode) {
        print('${LocaleKeys.sc_str_ai_avatar_error_crop_failed.tr}: $e');
      }
      MVToast.showError('${LocaleKeys.sc_str_ai_avatar_error_crop_failed.tr}: ${e.toString()}');
    }
  }

  // Upload Avatar按钮点击
  void onUploadAvatarClicked() {
    if (isAIGenerationInProgress.value) {
      MVToast.showError(LocaleKeys.sc_str_ai_avatar_generating.tr);
      return;
    }

    BPDeviceUtil.pickPhotoFromAlbum(
      cropIfIO: false,
      completion: (file) async {
        if (file != null) {
          try {
            if (kIsWeb) {
              // Web端直接使用原图
              rxAvatarImageUpload.value = BPFileUpload(fileSelected: file);
              _updateAvatar();
              MVToast.showSuccess(LocaleKeys.common_done.tr);
              return;
            }

            // 移动端进行裁切
            CroppedFile? croppedFile = await ImageCropper().cropImage(
              maxWidth: 1000,
              maxHeight: 1000,
              sourcePath: file.file!.path,
              aspectRatio: const CropAspectRatio(ratioX: 1, ratioY: 1),
              uiSettings: [
                AndroidUiSettings(
                  toolbarTitle: LocaleKeys.sc_str_ai_avatar_crop_avatar.tr,
                  toolbarColor: Colors.deepOrange,
                  toolbarWidgetColor: Colors.white,
                  initAspectRatio: CropAspectRatioPreset.square,
                  lockAspectRatio: true,
                ),
                IOSUiSettings(
                  title: LocaleKeys.sc_str_ai_avatar_crop_avatar.tr,
                  doneButtonTitle: LocaleKeys.common_done.tr,
                  cancelButtonTitle: LocaleKeys.common_cancel.tr,
                  aspectRatioPresets: [CropAspectRatioPreset.square],
                ),
              ],
            );

            if (croppedFile != null) {
              final croppedBytes = await croppedFile.readAsBytes();
              final croppedPath = croppedFile.path;

              rxAvatarImageUpload.value = BPFileUpload(
                fileSelected: BPFile(
                  name: _generateFileName(filePath: croppedPath, bytes: croppedBytes),
                  file: File(croppedPath),
                  bytes: croppedBytes,
                ),
              );

              _updateAvatar();
              MVToast.showSuccess(LocaleKeys.sc_str_ai_avatar_error_crop_success.tr);
            }
          } catch (e) {
            MVToast.showError('${LocaleKeys.sc_str_ai_avatar_error_crop_failed.tr}: ${e.toString()}');
          }
        }
      },
    );
  }

  // Download按钮点击
  void onDownloadClicked() {
    String? downloadUrl;

    // 优先下载当前显示的头像
    if (currentAvatarUrl.value.isNotEmpty) {
      if (currentAvatarUrl.value.startsWith('data:image/') || currentAvatarUrl.value.startsWith('http')) {
        downloadUrl = currentAvatarUrl.value;
      }
    }

    // 如果没有当前头像，尝试下载用户的原始头像
    if (downloadUrl == null) {
      final user = _accountModel;
      if (user?.fakeAvatar?.url != null) {
        downloadUrl = user!.fakeAvatar!.url!;
      }
    }

    if (downloadUrl != null) {
      _downloadImage(downloadUrl);
    } else {
      MVToast.showError('No avatar to download');
    }
  }

  Future<void> _downloadImage(String url) async {
    try {
      if (url.startsWith('data:image/')) {
        // 下载base64格式的图片
        await _downloadBase64Image(url);
      } else if (url.startsWith('http')) {
        // 下载网络图片
        await _downloadNetworkImage(url);
      } else if (url.startsWith('/')) {
        // 复制本地文件
        await _copyLocalImage(url);
      } else {
        MVToast.showError('Unsupported image format');
      }
    } catch (e) {
      MVToast.showError('Download failed: ${e.toString()}');
    }
  }

  Future<void> _downloadBase64Image(String base64Url) async {
    try {
      MVToast.showSuccess('Downloading...');

      // 解码base64
      final dataUri = base64Url;
      final base64Str = dataUri.substring(dataUri.indexOf(',') + 1);
      final bytes = base64Decode(base64Str);

      // 生成文件名
      final fileName = 'avatar_${DateTime.now().millisecondsSinceEpoch}.jpg';

      if (kIsWeb) {
        // Web端下载
        MVToast.showError('Web download not implemented yet');
      } else {
        // 移动端保存到相册
        final tempDir = await Directory.systemTemp.createTemp('download_');
        final tempFile = File('${tempDir.path}/$fileName');
        await tempFile.writeAsBytes(bytes);

        // 这里可以使用 gal 或 image_gallery_saver 包保存到相册
        // 现在只是保存到临时目录
        MVToast.showSuccess('Avatar saved to Downloads');
      }
    } catch (e) {
      MVToast.showError('Download failed: ${e.toString()}');
    }
  }

  Future<void> _downloadNetworkImage(String url) async {
    try {
      MVToast.showSuccess('Downloading...');

      final dio = Dio();
      final response = await dio.get(
        url,
        options: Options(responseType: ResponseType.bytes),
      );

      if (response.statusCode == 200) {
        final bytes = Uint8List.fromList(response.data);
        final fileName = 'avatar_${DateTime.now().millisecondsSinceEpoch}.jpg';

        if (kIsWeb) {
          // Web端下载
          MVToast.showError('Web download not implemented yet');
        } else {
          // 移动端保存到相册
          final tempDir = await Directory.systemTemp.createTemp('download_');
          final tempFile = File('${tempDir.path}/$fileName');
          await tempFile.writeAsBytes(bytes);

          MVToast.showSuccess('Avatar downloaded successfully');
        }
      } else {
        MVToast.showError('Download failed: HTTP ${response.statusCode}');
      }
    } catch (e) {
      MVToast.showError('Download failed: ${e.toString()}');
    }
  }

  Future<void> _copyLocalImage(String filePath) async {
    try {
      MVToast.showSuccess('Copying...');

      final sourceFile = File(filePath);
      if (await sourceFile.exists()) {
        final bytes = await sourceFile.readAsBytes();
        final fileName = 'avatar_${DateTime.now().millisecondsSinceEpoch}.jpg';

        final tempDir = await Directory.systemTemp.createTemp('download_');
        final tempFile = File('${tempDir.path}/$fileName');
        await tempFile.writeAsBytes(bytes);

        MVToast.showSuccess('Avatar copied successfully');
      } else {
        MVToast.showError('Source file not found');
      }
    } catch (e) {
      MVToast.showError('Copy failed: ${e.toString()}');
    }
  }

  void _updateAvatar() {
    if (rxAvatarImageUpload.value != null) {
      if (kIsWeb) {
        currentAvatarUrl.value = 'data:image/jpeg;base64,${base64Encode(rxAvatarImageUpload.value!.fileSelected.bytes)}';
      } else {
        currentAvatarUrl.value = rxAvatarImageUpload.value!.fileSelected.file!.path;
      }
    }
  }

  // 保存头像功能
  void onSaveAvatar() {
    if (!isValid) {
      MVToast.showError('Please select or generate an avatar first');
      return;
    }

    if (isSubmitting) return;

    // 收集需要上传的文件
    List<BPFileUpload<BPFile>> filesToUpload = [];

    if (rxAvatarImageUpload.value != null && rxAvatarImageUpload.value!.isNeedUpload) {
      filesToUpload.add(rxAvatarImageUpload.value!);
    }

    if (rxSplashImageUpload.value != null && rxSplashImageUpload.value!.isNeedUpload) {
      filesToUpload.add(rxSplashImageUpload.value!);
    }

    if (filesToUpload.isNotEmpty) {
      _uploader.cancel();
      _uploader.upload(
        items: filesToUpload,
        indicator: true,
        success: (items) async {
          await _saveAvatarToServer();
        },
        failure: (e) {
          catchedError(e);
        },
      );
    } else {
      _saveAvatarToServer();
    }
  }

  Future<void> _saveAvatarToServer() async {
    final idf = _submitTask.create();
    try {
      Map<String, dynamic> body = {};

      // 如果有上传的头像文件
      if (rxAvatarImageUpload.value != null && !rxAvatarImageUpload.value!.isNeedUpload) {
        // 创建 fakeAvatar
        BPURLModel fakeAvatar = BPURLModel(
          id: rxAvatarImageUpload.value!.uploadId ?? 0,
          url: rxAvatarImageUpload.value!.uploadUrl,
        );

        body['fakeAvatar'] = fakeAvatar;

        // 如果有上传的splashImage，使用上传后的splashImage，否则使用头像
        if (rxSplashImageUpload.value != null && !rxSplashImageUpload.value!.isNeedUpload) {
          BPURLModel splashImage = BPURLModel(
            id: rxSplashImageUpload.value!.uploadId ?? 0,
            url: rxSplashImageUpload.value!.uploadUrl,
          );
          body['splashImage'] = splashImage;
        } else {
          body['splashImage'] = fakeAvatar;
        }
      }

      // 兼容AI头像逻辑 - 如果没有上传文件但有AI头像，则不发送头像参数
      // 让API使用默认头像处理
      if (aiOriginalImageUrl.value.isNotEmpty && rxAvatarImageUpload.value == null) {
        // 不设置fakeAvatar，让API使用现有头像
        // 可以考虑将AI头像保存到服务器，但现在先跳过
      }

      // 调用正确的API来更新用户头像
      final res = await _callUpdateAPI(body);

      // 更新用户信息 - synchronizeAccountBaseInfo 会自动缓存 splashImage
      await BPSessionManager.instance.synchronizeAccountBaseInfo(res);

      // 等待splash image缓存完成后再更新SplashController状态
      await _waitForSplashImageCache(res);

      // 立即更新SplashController的状态，确保新的splash image能够立即生效
      _updateSplashControllerState();

      MVToast.showSuccess('Avatar saved successfully');
      Get.back();
    } catch (e) {
      catchedError(e);
    } finally {
      _submitTask.completed(idf: idf);
    }
  }

  Future<BPUserModel> _callUpdateAPI(Map<String, dynamic> body) async {
    // 如果有AI头像但没有上传的文件，使用saveAIAvatar API
    if (aiOriginalImageUrl.value.isNotEmpty && rxAvatarImageUpload.value == null) {
      // 为AI头像准备特定的body
      Map<String, dynamic> aiBody = {
        'imageData': aiOriginalImageUrl.value,
      };
      return await BPRestClient().saveAIAvatar(body: aiBody, cancelToken: _submitTask.task);
    }
    // 如果有上传的文件，使用editMeInfo API
    else if (body.isNotEmpty) {
      final userId = _accountModel?.id;
      if (userId == null) {
        throw Exception('User ID not found');
      }
      return await BPRestClient().editMeInfo(id: userId, body: body, cancelToken: _submitTask.task);
    }
    // 如果没有任何更新，抛出错误
    else {
      throw Exception('No avatar data to save');
    }
  }

  void _resetAIGenerationState() {
    isAIGenerationInProgress.value = false;
    aiGenerationStatusText.value = '';
    aiGenerationStep.value = AIGenerationStep.none;
    showAIGenerationDialog.value = false;
    canCancelGeneration.value = true;
    estimatedTimeRemaining.value = 0;
    _countdownTimer?.cancel();
    _statusPollingTimer?.cancel();
  }

  Future<void> _checkPendingAIGeneration() async {
    try {
      final userId = _accountModel?.id;
      if (userId == null) return;

      final storedTaskId = await _getStoredTaskId(userId);
      if (storedTaskId.isEmpty) return;

      final result = await BPRestClient().getAIAvatarTaskStatus(taskId: storedTaskId);

      if (result['success'] == true) {
        final status = result['status'];

        if (status == 'pending' || status == 'processing') {
          aiGenerationTaskId.value = storedTaskId;
          isAIGenerationInProgress.value = true;
          aiGenerationStep.value = status == 'pending' ? AIGenerationStep.pending : AIGenerationStep.processing;
          aiGenerationStatusText.value = status == 'pending' ? LocaleKeys.sc_str_ai_avatar_wait_time.tr : LocaleKeys.sc_str_ai_avatar_generating.tr;

          _startStatusPolling();
          MVToast.showSuccess(LocaleKeys.sc_str_ai_avatar_task_restored.tr);
        } else if (status == 'completed') {
          if (result['imageBase64'] != null) {
            final aiImageUrl = 'data:image/jpeg;base64,${result['imageBase64']}';
            aiOriginalImageUrl.value = aiImageUrl;
            currentAvatarUrl.value = aiImageUrl;

            _showAICompletionDialog();
            MVToast.showSuccess(LocaleKeys.sc_str_ai_avatar_task_completed.tr);
          }
          await _clearStoredTaskId(userId);
        } else {
          await _clearStoredTaskId(userId);
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('Check pending AI generation error: $e');
      }
    }
  }

  Future<void> _storeTaskId(int userId, String taskId) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final key = 'ai_avatar_task_$userId';
      await prefs.setString(key, taskId);
    } catch (e) {
      if (kDebugMode) {
        print('Error storing task ID: $e');
      }
    }
  }

  Future<String> _getStoredTaskId(int userId) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final key = 'ai_avatar_task_$userId';
      return prefs.getString(key) ?? '';
    } catch (e) {
      if (kDebugMode) {
        print('Error getting stored task ID: $e');
      }
      return '';
    }
  }

  Future<void> _clearStoredTaskId(int userId) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final key = 'ai_avatar_task_$userId';
      await prefs.remove(key);
    } catch (e) {
      if (kDebugMode) {
        print('Error clearing stored task ID: $e');
      }
    }
  }

  /// 等待splash image缓存完成
  Future<void> _waitForSplashImageCache(BPUserModel userModel) async {
    if (userModel.splashImage?.url == null) {
      if (kDebugMode) {
        print('_waitForSplashImageCache: no splash image URL, skipping');
      }
      return;
    }

    final sessionManager = BPSessionManager.instance;
    final maxWaitTime = 5000; // 最大等待5秒
    final checkInterval = 100; // 每100毫秒检查一次
    int elapsedTime = 0;

    if (kDebugMode) {
      print('_waitForSplashImageCache: starting to wait for splash image cache completion');
    }

    while (elapsedTime < maxWaitTime) {
      // 检查splash image是否已经被缓存
      if (sessionManager.hasSplashImage) {
        if (kDebugMode) {
          print('_waitForSplashImageCache: splash image cache completed after ${elapsedTime}ms');
        }
        return;
      }

      await Future.delayed(Duration(milliseconds: checkInterval));
      elapsedTime += checkInterval;

      if (kDebugMode && elapsedTime % 1000 == 0) {
        print('_waitForSplashImageCache: still waiting after ${elapsedTime}ms');
      }
    }

    if (kDebugMode) {
      print('_waitForSplashImageCache: timeout after ${maxWaitTime}ms, checking final state');
      print('_waitForSplashImageCache: final hasSplashImage=${sessionManager.hasSplashImage}');
    }
  }

  /// 更新SplashController的状态，使新的splash image能立即生效
  void _updateSplashControllerState() {
    try {
      // 尝试获取SplashController并更新其状态
      if (Get.isRegistered<SplashController>()) {
        final splashController = Get.find<SplashController>();
        splashController.refreshSplashImage();
        if (kDebugMode) {
          print('SplashController state updated successfully');
        }
      }
    } catch (e) {
      // 如果SplashController未注册或获取失败，忽略错误
      // 这是正常情况，因为用户可能已经离开了Splash页面
      if (kDebugMode) {
        print('SplashController not available for update: $e');
      }
    }
  }

  String _generateFileName({String? filePath, String? fileName, Uint8List? bytes}) {
    var name = (fileName ?? '').isNotEmpty ? fileName! : (filePath ?? '').split('/').last;
    name = name.removeAllWhitespace;
    if (name.isEmpty) {
      name = DateTime.now().millisecondsSinceEpoch.toString();
    }
    const nameMaxLength = 25;
    if (name.length > nameMaxLength) {
      name = name.substring(0, nameMaxLength - 5) + name.substring(name.length - 5);
    }
    return name;
  }
}
