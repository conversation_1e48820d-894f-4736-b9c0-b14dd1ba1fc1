import 'dart:io';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:image_cropper/image_cropper.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:device_info_plus/device_info_plus.dart';
import '../../../generated/locales.g.dart';
import '../../data/BPCommonModels.dart';
import '../multiple_platform/multiple_platform_tools_interface.dart';
import 'BPAlert.dart';
import 'BPMime.dart';

enum BPPlatform { web, ios, android, macOS, linux, windows, unknow }

class BPDeviceUtil {
  static bool get isWeb => BPDeviceUtil.currentPlatform == BPPlatform.web;
  static bool get isIOS => BPDeviceUtil.currentPlatform == BPPlatform.ios;
  static bool get isAndroid => BPDeviceUtil.currentPlatform == BPPlatform.android;

  static BPPlatform get currentPlatform {
    if (kIsWeb) {
      return BPPlatform.web;
    } else {
      if (Platform.isAndroid) {
        return BPPlatform.android;
      } else if (Platform.isIOS) {
        return BPPlatform.ios;
      } else if (Platform.isLinux) {
        return BPPlatform.linux;
      } else if (Platform.isMacOS) {
        return BPPlatform.macOS;
      } else if (Platform.isWindows) {
        return BPPlatform.windows;
      } else {
        return BPPlatform.unknow;
      }
    }
  }

  //IO platform only
  static Future<void> requestPhotosAccess(Function(bool canAccess) completion) async {
    if (kIsWeb) {
      completion(true);
      return;
    }
    final plugin = DeviceInfoPlugin();
    final androidInfo = BPDeviceUtil.isAndroid ? await plugin.androidInfo : null;
    var status = await (BPDeviceUtil.isIOS || ((androidInfo?.version.sdkInt ?? 0) >= 33) ? Permission.photos.status : Permission.storage.status);
    alertShow() {
      BPAlert.show(
        title: LocaleKeys.permission_photo_galley_alert_title.tr,
        content: LocaleKeys.permission_photo_galley_alert_content.tr,
        actionTitles: [
          LocaleKeys.common_continue.tr,
        ],
        highlightIndex: 0,
        completion: (index) {
          openAppSettings();
        },
      );
    }

    if (status.isPermanentlyDenied || status.isRestricted) {
      alertShow();
      completion(false);
    } else if (status.isDenied) {
      status = await (BPDeviceUtil.isIOS || ((androidInfo?.version.sdkInt ?? 0) >= 33) ? Permission.photos.request() : Permission.storage.request());
      if (status.isGranted || status.isLimited) {
        completion(true);
      } else {
        alertShow();
        completion(false);
      }
    } else if (status.isGranted || status.isLimited) {
      completion(true);
    }
  }

  //IO platform only
  static Future<void> requestCameraAccess(Function(bool canAccess) completion) async {
    if (kIsWeb) {
      completion(true);
      return;
    }
    alertShow() {
      BPAlert.show(
        title: LocaleKeys.permission_camera_alert_title.tr,
        content: LocaleKeys.permission_camera_alert_content.tr,
        actionTitles: [
          LocaleKeys.common_continue.tr,
        ],
        highlightIndex: 0,
        completion: (index) {
          openAppSettings();
        },
      );
    }

    var status = await Permission.camera.status;
    // debugPrint('Permission.camera.status : $status');
    if (status.isPermanentlyDenied || status.isRestricted) {
      alertShow();
      completion(false);
    } else if (status.isDenied) {
      status = await Permission.camera.request();
      if (status.isGranted || status.isLimited) {
        completion(true);
      } else {
        alertShow();
        completion(false);
      }
    } else if (status.isGranted || status.isLimited) {
      completion(true);
    }
  }

  //IO platform only
  static Future<void> requestMicrophoneAccess(Function(bool canAccess) completion) async {
    if (kIsWeb) {
      return;
    }
    alertShow() {
      BPAlert.show(
        title: LocaleKeys.permission_mic_alert_title.tr,
        content: LocaleKeys.permission_mic_alert_content.tr,
        actionTitles: [
          LocaleKeys.common_continue.tr,
        ],
        highlightIndex: 0,
        completion: (index) {
          openAppSettings();
        },
      );
    }

    var status = await Permission.microphone.status;
    // debugPrint('Permission.microphone.status : $status');
    if (status.isPermanentlyDenied || status.isRestricted) {
      alertShow();
      completion(false);
    } else if (status.isDenied) {
      status = await Permission.microphone.request();
      if (status.isGranted || status.isLimited) {
        completion(true);
      } else {
        alertShow();
        completion(false);
      }
    } else if (status.isGranted || status.isLimited) {
      completion(true);
    }
  }

  static String _fileName({String? filePath, String? fileName, Uint8List? bytes}) {
    var name = (fileName ?? '').isNotEmpty ? fileName! : (filePath ?? '').split('/').last;
    name = name.removeAllWhitespace;
    if (name.isEmpty) {
      name = DateTime.now().millisecondsSinceEpoch.toString();
    }
    const nameMaxLength = 25;
    if (name.length > nameMaxLength) {
      name = name.substring(0, nameMaxLength - 5) + name.substring(name.length - 5);
    }
    var type = '';
    if (name.contains('.')) {
      type = name.split('.').last;
    }
    if (type.isEmpty && bytes != null) {
      final mime = BPMime.lookupMimeType(name, headerBytes: bytes) ?? '';
      name = '$name.${BPMime.extensionFromMime(mime)}';
    }
    return name;
  }

  static void pickPhotoFromAlbum({BuildContext? context, bool cropIfIO = true, int cropMaxWidth = 1000, int cropMaxHeight = 1000, required Function(BPFile? file) completion}) async {
    // Android 平台使用 file_picker 作为兼容方案
    if (!kIsWeb && Platform.isAndroid) {
      try {
        FilePickerResult? result = await FilePicker.platform.pickFiles(
          type: FileType.image,
        );

        if (result != null && result.files.isNotEmpty) {
          final element = result.files.first;
          final filePath = element.path;

          if (filePath != null) {
            final bytes = await File(filePath).readAsBytes();

            // 如果需要裁剪且不是 Web 平台
            if (cropIfIO && !kIsWeb) {
              CroppedFile? croppedFile = await ImageCropper().cropImage(
                maxWidth: cropMaxWidth,
                maxHeight: cropMaxHeight,
                sourcePath: filePath,
                uiSettings: [
                  AndroidUiSettings(toolbarTitle: LocaleKeys.common_edit.tr, toolbarColor: Colors.deepOrange, toolbarWidgetColor: Colors.white, initAspectRatio: CropAspectRatioPreset.original, lockAspectRatio: false, aspectRatioPresets: [CropAspectRatioPreset.square, CropAspectRatioPreset.ratio3x2, CropAspectRatioPreset.original, CropAspectRatioPreset.ratio4x3, CropAspectRatioPreset.ratio16x9]),
                  if (context != null) ...[WebUiSettings(context: context)],
                ],
              );
              if (croppedFile != null) {
                final croppedBytes = await croppedFile.readAsBytes();
                final croppedPath = croppedFile.path;
                completion(BPFile(name: _fileName(filePath: croppedPath, bytes: croppedBytes), file: File(croppedPath), bytes: croppedBytes));
                return;
              }
            } else {
              // 不需要裁剪，直接返回
              completion(BPFile(
                name: element.name,
                file: File(filePath),
                bytes: bytes,
              ));
              return;
            }
          }
        }
        completion(null);
        return;
      } catch (e) {
        // file_picker 失败，回退到原有方法
      }
    }

    // 原有的权限请求和相册选择逻辑（iOS、Web 和 Android 回退）
    requestPhotosAccess((canAccess) {
      if (canAccess) {
        final pt = MultiplePlatformTools();
        pt.showImagePicker(completion: (path, name, bytes) async {
          if (bytes != null) {
            if (kIsWeb || !cropIfIO || path == null) {
              completion(BPFile(name: _fileName(filePath: path, fileName: name, bytes: bytes), file: path == null ? null : File(path), bytes: bytes));
              return;
            } else {
              CroppedFile? croppedFile = await ImageCropper().cropImage(
                maxWidth: cropMaxWidth,
                maxHeight: cropMaxHeight,
                sourcePath: path,
                uiSettings: [
                  AndroidUiSettings(toolbarTitle: LocaleKeys.common_edit.tr, toolbarColor: Colors.deepOrange, toolbarWidgetColor: Colors.white, initAspectRatio: CropAspectRatioPreset.original, lockAspectRatio: false, aspectRatioPresets: [CropAspectRatioPreset.square, CropAspectRatioPreset.ratio3x2, CropAspectRatioPreset.original, CropAspectRatioPreset.ratio4x3, CropAspectRatioPreset.ratio16x9]),
                  IOSUiSettings(title: LocaleKeys.common_edit.tr, doneButtonTitle: LocaleKeys.common_ok.tr, cancelButtonTitle: LocaleKeys.common_cancel.tr, aspectRatioPresets: [CropAspectRatioPreset.square, CropAspectRatioPreset.ratio3x2, CropAspectRatioPreset.original, CropAspectRatioPreset.ratio4x3, CropAspectRatioPreset.ratio16x9]),
                  if (context != null) ...[WebUiSettings(context: context)],
                ],
              );
              if (croppedFile != null) {
                final bytes = await croppedFile.readAsBytes();
                final path = croppedFile.path;
                completion(BPFile(name: _fileName(filePath: path, bytes: bytes), file: File(path), bytes: bytes));
                return;
              }
            }
          }
          completion(null);
        });
      }
    });
  }

  static void pickPhotoWithSourcePicker({BuildContext? context, bool cropIfIO = true, int cropMaxWidth = 1000, int cropMaxHeight = 1000, required Function(BPFile? file) completion}) async {
    requestPhotosAccess((canAccess) {
      if (canAccess) {
        final pt = MultiplePlatformTools();
        pt.showImageSourcePicker(
          context: context,
          completion: (path, name, bytes) async {
            if (bytes != null) {
              if (kIsWeb || !cropIfIO || path == null) {
                completion(BPFile(name: _fileName(filePath: path, fileName: name, bytes: bytes), file: path == null ? null : File(path), bytes: bytes));
                return;
              } else {
                CroppedFile? croppedFile = await ImageCropper().cropImage(
                  maxWidth: cropMaxWidth,
                  maxHeight: cropMaxHeight,
                  sourcePath: path,
                  uiSettings: [
                    AndroidUiSettings(
                      toolbarTitle: LocaleKeys.common_edit.tr,
                      toolbarColor: Colors.deepOrange,
                      toolbarWidgetColor: Colors.white,
                      initAspectRatio: CropAspectRatioPreset.original,
                      lockAspectRatio: false,
                      aspectRatioPresets: [
                        CropAspectRatioPreset.square,
                        CropAspectRatioPreset.ratio3x2,
                        CropAspectRatioPreset.original,
                        CropAspectRatioPreset.ratio4x3,
                        CropAspectRatioPreset.ratio16x9,
                      ],
                    ),
                    IOSUiSettings(
                      title: LocaleKeys.common_edit.tr,
                      doneButtonTitle: LocaleKeys.common_done.tr,
                      cancelButtonTitle: LocaleKeys.common_cancel.tr,
                      aspectRatioPresets: [
                        CropAspectRatioPreset.square,
                        CropAspectRatioPreset.ratio3x2,
                        CropAspectRatioPreset.original,
                        CropAspectRatioPreset.ratio4x3,
                        CropAspectRatioPreset.ratio16x9,
                      ],
                    ),
                    if (context != null) ...[
                      WebUiSettings(
                        context: context,
                      ),
                    ],
                  ],
                );
                if (croppedFile != null) {
                  final bytes = await croppedFile.readAsBytes();
                  final path = croppedFile.path;
                  completion(
                    BPFile(
                      name: _fileName(filePath: path, bytes: bytes),
                      file: File(path),
                      bytes: bytes,
                    ),
                  );
                  return;
                }
              }
            }
            completion(null);
          },
        );
      }
    });
  }

  static void pickVideoFromAlbum({Duration? maxDuration, required Function(BPFile? file) completion}) async {
    // Android 平台使用 file_picker 作为兼容方案
    if (!kIsWeb && Platform.isAndroid) {
      try {
        FilePickerResult? result = await FilePicker.platform.pickFiles(
          type: FileType.video,
        );

        if (result != null && result.files.isNotEmpty) {
          final element = result.files.first;
          final filePath = element.path;

          if (filePath != null) {
            final bytes = await File(filePath).readAsBytes();
            completion(BPFile(
              name: element.name,
              file: File(filePath),
              bytes: bytes,
            ));
            return;
          }
        }
        completion(null);
        return;
      } catch (e) {
        // file_picker 失败，回退到原有方法
      }
    }

    // 原有的权限请求和相册选择逻辑（iOS、Web 和 Android 回退）
    requestPhotosAccess((canAccess) {
      if (canAccess) {
        final pt = MultiplePlatformTools();
        pt.showVideoPicker(
            maxDuration: maxDuration,
            completion: (path, name, bytes) async {
              if (bytes != null) {
                completion(BPFile(name: _fileName(filePath: path, fileName: name, bytes: bytes), file: path == null ? null : File(path), bytes: bytes));
                return;
              }
              completion(null);
            });
      }
    });
  }

  static void pickMediaFromAlbum({required Function(BPFile? file) completion}) async {
    // Android 平台使用 file_picker 作为兼容方案
    if (!kIsWeb && Platform.isAndroid) {
      try {
        FilePickerResult? result = await FilePicker.platform.pickFiles(
          type: FileType.media,
        );

        if (result != null && result.files.isNotEmpty) {
          final element = result.files.first;
          final filePath = element.path;

          if (filePath != null) {
            final bytes = await File(filePath).readAsBytes();
            completion(BPFile(
              name: element.name,
              file: File(filePath),
              bytes: bytes,
            ));
            return;
          }
        }
        completion(null);
        return;
      } catch (e) {
        // file_picker 失败，回退到原有方法
      }
    }

    // 原有的权限请求和相册选择逻辑（iOS、Web 和 Android 回退）
    requestPhotosAccess((canAccess) {
      if (canAccess) {
        final pt = MultiplePlatformTools();
        pt.showMediaPicker(completion: (path, name, bytes) async {
          if (bytes != null) {
            completion(BPFile(name: _fileName(filePath: path, fileName: name, bytes: bytes), file: path == null ? null : File(path), bytes: bytes));
            return;
          }
          completion(null);
        });
      }
    });
  }

  static Future<void> pickFile({
    int singleMaxSize = 1024 * 1024 * 1024, //byte
    required FileType fileType,
    List<String>? allowedExtensions,
    required Function(bool isTooLarge, BPFile? file) completion,
  }) async {
    FilePickerResult? result = await FilePicker.platform.pickFiles(
      allowCompression: true,
      type: fileType,
      allowedExtensions: allowedExtensions,
    );
    if (result != null && result.files.isNotEmpty) {
      final element = result.files.first;
      if (element.size < (singleMaxSize)) {
        final filePath = kIsWeb ? null : element.path;
        if (((!kIsWeb && filePath != null) || (kIsWeb && element.bytes != null))) {
          var bytes = element.bytes;
          if (!kIsWeb && filePath != null && bytes == null) {
            bytes ??= await File(filePath).readAsBytes();
          }
          if (bytes != null) {
            completion(false, BPFile(name: _fileName(filePath: filePath ?? element.name, fileName: element.name, bytes: bytes), file: filePath == null ? null : File(filePath), bytes: bytes));
          } else {
            completion(false, null);
          }
          return;
        }
      } else {
        completion(true, null);
        return;
      }
    }
    completion(false, null);
    return;
  }

  // static Future<List<BPFile>?> test() async {
  //   FilePickerResult? result = await FilePicker.platform.pickFiles(
  //     type: FileType.image,
  //     allowMultiple: true,
  //   );

  //   if (result != null) {
  //     List<BPFile> files = [];

  //     for (var element in result.files) {
  //       if ((element.path?.isNotEmpty ?? false)) {
  //         files.add(BPFile(
  //           name: element.name,
  //           file: File(element.path!),
  //           bytes: element.bytes ?? Uint8List.fromList([]),
  //         ));
  //       }
  //     }

  //     return files;
  //   } else {
  //     return null;
  //   }
  // }
}
