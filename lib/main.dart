import 'package:country_ip/country_ip.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import 'app/modules/NotFound/bindings/not_found_binding.dart';
import 'app/modules/NotFound/views/not_found_view.dart';
import 'app/routes/app_pages.dart';
import 'app/services/deeplink_service.dart';
import 'app/shared/BPLaunching.dart';

import 'app/shared/components/BPColorUtil.dart';
import 'app/shared/components/BPLanguageUtil.dart';
import 'app/shared/components/BPRouteObserver.dart';
import 'generated/locales.g.dart';

CountryResponse? countryIpResponse;
Future<void> main() async {
  // WidgetsFlutterBinding.ensureInitialized();
  await BPLaunching.ensureInitialized();

  countryIpResponse = await CountryIp.find();

  runApp(
    ScreenUtilInit(
      designSize: const Size(375, 812),
      minTextAdapt: true,
      splitScreenMode: true,
      useInheritedMediaQuery: true,
      builder: (context, child) {
        return GestureDetector(
          onTap: () => FocusScope.of(context).requestFocus(FocusNode()),
          child: GetMaterialApp(
            title: "Starchex",
            navigatorObservers: [BPRouteObserverUtil().routeObserver],
            translationsKeys: AppTranslation.translations,
            supportedLocales: const [Locale('en', 'US'), Locale('ko', 'KR'), Locale('zh', 'CN')],
            fallbackLocale: const Locale('en', 'US'),
            localizationsDelegates: const [GlobalMaterialLocalizations.delegate, GlobalWidgetsLocalizations.delegate, GlobalCupertinoLocalizations.delegate],
            locale: BPLanguageUtil.localeCurrent,
            theme: ThemeData(
              expansionTileTheme: const ExpansionTileThemeData(
                childrenPadding: EdgeInsets.zero,
                tilePadding: EdgeInsets.zero,
              ),
              fontFamily: 'AppleSDGothicNeo',
              shadowColor: const Color(0x00000000),
              dividerColor: const Color(0xFFE4E4E4),
              scaffoldBackgroundColor: Colors.white,

              // Buttons Themes
              textButtonTheme: TextButtonThemeData(
                style: ButtonStyle(
                  foregroundColor: WidgetStateProperty.resolveWith((states) {
                    if (states.contains(WidgetState.disabled)) {
                      return const Color(0xFF949097);
                    }
                    return BPColor.gold;
                  }),
                ),
              ),
            ),
            initialRoute: AppPages.INITIAL,
            getPages: AppPages.routes,
            unknownRoute: GetPage(
              name: Routes.NOT_FOUND,
              page: () => const NotFoundView(),
              binding: NotFoundBinding(),
            ),
            onUnknownRoute: (settings) {
              debugPrint('onUnknownRoute:$settings');
              return null;
            },
            builder: EasyLoading.init(),
            initialBinding: InitBinding(),
          ),
        );
      },
    ),
  );

  Future<void>.delayed(const Duration(milliseconds: 500), () async {
    await BPLaunching.fetchForking();
  });
}

class InitBinding extends Bindings {
  @override
  void dependencies() {
    // Initialize DeepLink Service
    Get.put(DeepLinkService(), permanent: true);
  }
}
