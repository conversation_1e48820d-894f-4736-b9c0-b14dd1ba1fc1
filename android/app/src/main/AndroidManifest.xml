<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.starchex.app">
    <queries>
        <provider android:authorities="com.facebook.katana.provider.PlatformProvider" />
    </queries>

    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.CAMERA" />
    <uses-permission android:name="android.permission.USE_BIOMETRIC" />
    <!--    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE" />-->
    <!-- <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" /> -->
    <uses-permission android:name="android.permission.READ_MEDIA_AUDIO" />
    <!-- Always include this permission -->
    <!-- <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" /> -->

    <!-- Include only if your app benefits from precise location access. -->
    <!-- <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" /> -->

    <application
        android:label="Starchex"
        android:name="com.starchex.app.MeetokApplication"
        android:icon="@mipmap/launcher_icon"
        android:requestLegacyExternalStorage="true"
        android:usesCleartextTraffic="true">
        <activity
            android:name="com.starchex.app.MainActivity"
            android:exported="true"
            android:launchMode="singleTop"
            android:theme="@style/LaunchTheme"
            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
            android:hardwareAccelerated="true"
            android:windowSoftInputMode="adjustResize">
            <!-- Specifies an Android theme to apply to this Activity as soon as
                 the Android process has started. This theme is visible to the user
                 while the Flutter UI initializes. After that, this theme continues
                 to determine the Window background behind the Flutter UI. -->
            <meta-data
                android:name="io.flutter.embedding.android.NormalTheme"
                android:resource="@style/NormalTheme" />
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data android:scheme="starchex" />
            </intent-filter>
        </activity>
        <!-- ZaloFlutter start -->
        <!-- <meta-data
            android:name="com.zing.zalo.zalosdk.appID"
            android:value="@string/zalo_flutter_app_id" />
        <activity
            android:name="com.zing.zalo.zalosdk.oauth.BrowserLoginActivity"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data android:scheme="@string/zalo_flutter_app_id_protocol" />
            </intent-filter>
        </activity> -->
        <!-- ZaloFlutter end -->
        <!-- Don't delete the meta-data below.
             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java -->
        <meta-data
            android:name="flutterEmbedding"
            android:value="2" />
        <meta-data
            android:name="com.google.android.geo.API_KEY"
            android:value="AIzaSyAgVU4rF9ZD5cZkL7yY06ZVn0WEnvRtXUw" />
        
        <!-- Image Cropper Activity -->
        <activity
            android:name="com.yalantis.ucrop.UCropActivity"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.AppCompat.Light.NoActionBar"/>
    </application>
</manifest>
